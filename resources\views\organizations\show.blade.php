<x-layouts.unilink-layout>
    <!-- Organization Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
        <!-- Cover Image -->
        <div class="h-48 bg-gradient-to-r from-blue-500 to-purple-600 relative">
            @if($organization->cover_image)
                <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($organization->cover_image) }}" alt="{{ $organization->name }}" class="w-full h-full object-cover">
            @endif
            
            <!-- Action Buttons -->
            <div class="absolute top-4 right-4 flex space-x-2">
                @auth
                    @if($userMembership)
                        @if($userMembership->pivot->status === 'pending')
                            <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                                Pending Approval
                            </span>
                        @elseif($userMembership->pivot->status === 'active')
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                {{ ucfirst($userMembership->pivot->role) }}
                            </span>
                            @if($userMembership->pivot->role !== 'president')
                                <form action="{{ route('organizations.leave', $organization) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to leave this organization?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium hover:bg-red-700">
                                        Leave
                                    </button>
                                </form>
                            @endif
                        @endif
                    @else
                        <form action="{{ route('organizations.join', $organization) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700">
                                Join Organization
                            </button>
                        </form>
                    @endif
                    
                    @if(auth()->user()->isAdmin() || $organization->created_by === auth()->id() || ($userMembership && in_array($userMembership->pivot->role, ['officer', 'president', 'vice_president', 'secretary', 'treasurer'])))
                        <a href="{{ route('organizations.edit', $organization) }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700">
                            Edit
                        </a>
                    @endif

                    @if($userMembership && in_array($userMembership->pivot->role, ['president', 'vice_president', 'secretary', 'treasurer', 'officer']))
                        <a href="{{ route('organizations.dashboard', $organization) }}" class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700">
                            Dashboard
                        </a>
                    @endif

                    @if($userMembership && $userMembership->pivot->role === 'president')
                        <a href="{{ route('organizations.officers', $organization) }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700">
                            Manage Officers
                        </a>
                    @endif
                @endauth
            </div>
        </div>

        <!-- Organization Info -->
        <div class="p-6">
            <div class="flex items-start space-x-4">
                <!-- Logo -->
                <div class="flex-shrink-0 z-50">
                    <div class="w-20 h-20 bg-white rounded-lg shadow-md flex items-center justify-center -mt-13 border-4 border-white">
                        @if($organization->logo)
                            <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($organization->logo) }}" alt="{{ $organization->name }}" class="w-16 h-16 rounded-lg object-cover">
                        @else
                            <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span class="text-blue-600 font-bold text-lg">{{ substr($organization->name, 0, 2) }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Organization Details -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <h1 class="text-2xl font-bold text-gray-900">{{ $organization->name }}</h1>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $organization->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ ucfirst($organization->status) }}
                        </span>
                    </div>
                    
                    <p class="text-gray-600 mt-2">{{ $organization->description }}</p>
                    
                    <!-- Stats -->
                    <div class="flex flex-wrap items-center gap-x-6 gap-y-2 mt-4 text-sm text-gray-500">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            {{ $organization->activeMembers->count() }} members
                        </div>
                        @if($organization->school || $organization->campus)
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                                </svg>
                                <span class="truncate">
                                    @if($organization->school)
                                        {{ $organization->school->abbreviation ?? $organization->school->name }}
                                        @if($organization->campus)
                                            - {{ $organization->campus->name }}
                                        @endif
                                    @elseif($organization->campus)
                                        {{ $organization->campus->name }}
                                    @endif
                                </span>
                            </div>
                        @endif
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <span class="truncate">Created by {{ $organization->creator->name }}</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                            </svg>
                            {{ $organization->created_at->format('M Y') }}
                        </div>
                    </div>

                    <!-- Contact Info -->
                    @if($organization->email || $organization->phone || $organization->website)
                        <div class="flex items-center space-x-4 mt-4">
                            @if($organization->email)
                                <a href="mailto:{{ $organization->email }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                    {{ $organization->email }}
                                </a>
                            @endif
                            @if($organization->phone)
                                <a href="tel:{{ $organization->phone }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                    {{ $organization->phone }}
                                </a>
                            @endif
                            @if($organization->website)
                                <a href="{{ $organization->website }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                    Website
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Post Creation Area -->
            @if(auth()->check() && $organization->userCanPost(auth()->user()))
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center space-x-4">
                        <img src="{{ auth()->user()->getAvatarUrl(40) }}"
                             alt="{{ auth()->user()->name }}"
                             class="w-10 h-10 rounded-full">
                        <button onclick="openOrgPostModal()"
                                class="flex-1 text-left px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-full text-gray-500 transition-colors">
                            Share something in {{ $organization->name }}...
                        </button>
                        <button onclick="openOrgPostModal()"
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                        </button>
                    </div>

                    <!-- Quick Action Buttons -->
                    <div class="flex items-center justify-around mt-4 pt-4 border-t border-gray-200">
                        <button onclick="openOrgPostModal()" class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                            </svg>
                            <span class="text-sm font-medium">Announcement</span>
                        </button>
                        <button onclick="openOrgPostModal()" class="flex items-center space-x-2 text-gray-600 hover:text-green-600 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                            </svg>
                            <span class="text-sm font-medium">Event</span>
                        </button>
                        <button onclick="openOrgPostModal()" class="flex items-center space-x-2 text-gray-600 hover:text-purple-600 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                            </svg>
                            <span class="text-sm font-medium">Update</span>
                        </button>
                    </div>
                </div>
            @endif

            <!-- Recent Posts -->
            <div class="rounded-lg">
                <div class="bg-white rounded-lg p-4 mb-2 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Recent Posts</h2>
                </div>
                
                @if($organization->posts->count() > 0)
                    <div class="space-y-6">
                        @foreach($organization->posts as $post)
                            <x-post-card :post="$post" />
                        @endforeach
                    </div>
                @else
                    <div class="p-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No posts yet</h3>
                        <p class="mt-1 text-sm text-gray-500">This organization hasn't posted anything yet.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Officers -->
            @if($organization->officers->count() > 0)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Officers</h3>
                </div>

                <div class="p-4">
                    <div class="space-y-3">
                        @foreach($organization->officers->sortBy(function($officer) {
                            return match($officer->pivot->role) {
                                'president' => 1,
                                'vice_president' => 2,
                                'secretary' => 3,
                                'treasurer' => 4,
                                'officer' => 5,
                                default => 6
                            };
                        }) as $officer)
                            <div class="flex items-center space-x-3">
                                <img class="h-10 w-10 rounded-full" src="{{ $officer->getAvatarUrl(40) }}" alt="{{ $officer->name }}">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $officer->name }}</p>
                                    <p class="text-xs text-gray-500">
                                        {{ $officer->pivot->custom_role_title ?: match($officer->pivot->role) {
                                            'president' => 'President',
                                            'vice_president' => 'Vice President',
                                            'secretary' => 'Secretary',
                                            'treasurer' => 'Treasurer',
                                            'officer' => 'Officer',
                                            default => ucfirst(str_replace('_', ' ', $officer->pivot->role))
                                        } }}
                                    </p>
                                </div>
                                @if($officer->pivot->role === 'president')
                                    <svg class="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M9.664 1.319a.75.75 0 01.672 0 41.059 41.059 0 018.198 5.424.75.75 0 01-.254 1.285 31.372 31.372 0 00-7.86 3.83.75.75 0 01-.84 0 31.508 31.508 0 00-2.08-1.287V9.394c0-.244.116-.463.302-.592a35.504 35.504 0 013.305-*********** 0 00-.714-1.319 37 37 0 00-3.446 2.12A2.216 2.216 0 006 9.393v.38a31.293 31.293 0 00-4.28-1.746.75.75 0 01-.254-1.285 41.059 41.059 0 018.198-5.424zM6 11.459a29.848 29.848 0 00-2.455-1.158 41.029 41.029 0 00-.39 *********** 0 00.419.74c.528.256 1.046.53 1.554.82-.21-.899-.455-1.746-.754-2.516zm9.909-1.158A29.848 29.848 0 0014 11.459c-.299.77-.544 1.617-.754 2.516.508-.29 1.026-.564 1.554-.82a.75.75 0 00.419-.74 41.029 41.029 0 00-.39-3.114z" clip-rule="evenodd" />
                                    </svg>
                                @else
                                    <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                    </svg>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Members -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Members ({{ $organization->activeMembers->count() }})</h3>
                </div>
                
                <div class="p-4">
                    <div class="space-y-3">
                        @foreach($organization->activeMembers->take(10) as $member)
                            <div class="flex items-center space-x-3">
                                <img class="h-8 w-8 rounded-full" src="{{ $member->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($member->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($member->name) . '&color=7F9CF5&background=EBF4FF' }}" alt="{{ $member->name }}">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $member->name }}</p>
                                    <p class="text-xs text-gray-500">{{ ucfirst($member->pivot->role) }}</p>
                                </div>
                                @if($member->pivot->role === 'president')
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                        President
                                    </span>
                                @elseif(in_array($member->pivot->role, ['vice_president', 'secretary', 'treasurer', 'officer']))
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $member->pivot->custom_role_title ?: match($member->pivot->role) {
                                            'vice_president' => 'Vice President',
                                            'secretary' => 'Secretary',
                                            'treasurer' => 'Treasurer',
                                            'officer' => 'Officer',
                                            default => ucfirst(str_replace('_', ' ', $member->pivot->role))
                                        } }}
                                    </span>
                                @endif
                            </div>
                        @endforeach
                        
                        @if($organization->activeMembers->count() > 10)
                            <div class="text-center pt-2">
                                <button class="text-sm text-blue-600 hover:text-blue-800">
                                    View all {{ $organization->activeMembers->count() }} members
                                </button>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Organization Post Creation Modal -->
    @if(auth()->check() && $organization->userCanPost(auth()->user()))
        <x-org-post-creation-modal :organization="$organization" />
    @endif
</x-layouts.unilink-layout>
